# AI Coding Agent Prompt: Modern Node.js Reviews Service

## Project Overview
You are tasked with building a **modern, scalable Node.js/Express.js reviews and ratings service** from scratch. This is a greenfield project that should leverage current best practices, optimal API design, and modern Node.js ecosystem tools to create a high-performance, maintainable service.

## Core Technology Stack
- **Runtime**: Python 3.8+ 
- **Framework**: FastAPI and Pydantic
- **Database**: AWS DynamoDB (primary) + Redis (caching/sessions)
- **Storage**: AWS S3 + CloudFront CDN for media files
- **Background Jobs**: AWS Lambda + SQS for task queues
- **local Background Jobs**: FastAPI background tasks
- **External APIs**: Google Translate API for multilingual support
- **Testing**: Pytest + Mock for unit and integration tests
- **Development**: Docker containers for consistent local environment

## Service Objectives

### 1. **Modern Review Management System**
- **Intuitive CRUD APIs**: RESTful endpoints with clear, consistent naming
- **Rich Media Support**: Handle images and videos with smart compression and optimization
- **Flexible Filtering**: Advanced search and filtering capabilities
- **Real-time Updates**: WebSocket support for live review updates
- **Smart Validation**: Comprehensive input validation with helpful error messages

### 2. **Intelligent Rating Analytics**
- **Real-time Statistics**: Live calculation of product ratings and trends
- **Advanced Analytics**: Rating distributions, trends over time, sentiment analysis
- **Bulk Operations**: Efficient batch processing for multiple products
- **Smart Caching**: Intelligent cache invalidation and warming strategies

### 3. **Seamless Multilingual Experience**
- **Auto-translation**: Intelligent language detection and translation
- **Translation Quality**: Confidence scoring and fallback mechanisms
- **Batch Processing**: Efficient background translation workflows
- **Language Preferences**: User-specific language settings and preferences

### 4. **Enterprise-Grade Performance**
- **Sub-100ms Response Times**: Optimized for speed with smart caching
- **Horizontal Scalability**: Designed for high-traffic, multi-region deployment
- **Efficient Background Processing**: Non-blocking operations for heavy tasks
- **Resource Optimization**: Memory and CPU efficient implementations

## Modern API Design Principles

### **RESTful Resource Design**
```typescript
// Clean, intuitive endpoint structure
POST   /api/v1/reviews                    // Create review
GET    /api/v1/reviews/:id                // Get specific review
PUT    /api/v1/reviews/:id                // Update review
DELETE /api/v1/reviews/:id                // Delete review
GET    /api/v1/reviews                    // List reviews with filters

GET    /api/v1/products/:id/reviews       // Get product reviews
GET    /api/v1/products/:id/statistics    // Get product rating stats
POST   /api/v1/products/statistics/bulk   // Bulk statistics retrieval

POST   /api/v1/reviews/:id/translate      // Translate specific review
GET    /api/v1/reviews/:id/media          // Get review media
POST   /api/v1/reviews/:id/media          // Upload media to review
```

### **Modern Response Format**
```typescript
// Consistent, informative response structure
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, any>;
  };
  meta?: {
    pagination?: PaginationMeta;
    timestamp: string;
    request_id: string;
  };
}

// Rich pagination with cursor-based approach
interface PaginationMeta {
  total_count?: number;
  page_size: number;
  has_next: boolean;
  has_previous: boolean;
  next_cursor?: string;
  previous_cursor?: string;
}
```

## Enhanced Data Models

### **Modern Review Model**
```typescript
interface Review {
  id: string;                           // UUID primary key
  product_id: string;
  user_id: string;
  rating: number;                       // 1-5 stars
  title?: string;                       // Optional review title
  content: string;                      // Review text content
  language: string;                     // ISO 639-1 language code
  translations?: Record<string, string>; // Translated content
  country_code: string;                 // ISO 3166-1 alpha-2
  status: 'draft' | 'published' | 'moderated' | 'rejected';
  media_items: MediaItem[];
  metadata: {
    verified_purchase?: boolean;
    helpful_votes: number;
    total_votes: number;
    sentiment_score?: number;           // AI-powered sentiment analysis
  };
  timestamps: {
    created_at: string;
    updated_at: string;
    published_at?: string;
  };
}
```

### **Enhanced Statistics Model**
```typescript
interface ProductStatistics {
  product_id: string;
  overall_rating: {
    average: number;
    count: number;
    distribution: Record<1|2|3|4|5, number>;
  };
  trends: {
    last_30_days: {
      average: number;
      count: number;
      change_percentage: number;
    };
    last_7_days: {
      average: number;
      count: number;
      change_percentage: number;
    };
  };
  demographics: {
    by_country: Record<string, { average: number; count: number }>;
    by_verified_purchase: { average: number; count: number };
  };
  last_updated: string;
}
```

### **Smart Media Handling**
```typescript
interface MediaItem {
  id: string;
  type: 'image' | 'video';
  original_url: string;
  optimized_urls: {
    thumbnail: string;
    small: string;
    medium: string;
    large: string;
  };
  metadata: {
    file_size: number;
    dimensions?: { width: number; height: number };
    duration?: number;                  // For videos
    mime_type: string;
  };
  processing_status: 'pending' | 'completed' | 'failed';
}
```

## Modern Architecture Patterns

### **Service Layer with Dependency Injection**
```typescript
// Clean service interfaces
interface IReviewService {
  createReview(data: CreateReviewDto): Promise<Review>;
  getReview(id: string): Promise<Review | null>;
  updateReview(id: string, data: UpdateReviewDto): Promise<Review>;
  deleteReview(id: string): Promise<void>;
  searchReviews(filters: ReviewFilters): Promise<PaginatedResult<Review>>;
  translateReview(id: string, targetLanguage: string): Promise<Review>;
}

interface IStatisticsService {
  getProductStatistics(productId: string): Promise<ProductStatistics>;
  getBulkStatistics(productIds: string[]): Promise<Record<string, ProductStatistics>>;
  updateStatistics(productId: string): Promise<void>;
  getStatisticsTrends(productId: string, period: string): Promise<StatisticsTrend>;
}
```

### **Event-Driven Architecture**
```typescript
// Modern event system for loose coupling
interface ReviewEvents {
  'review.created': { review: Review };
  'review.updated': { review: Review; changes: Partial<Review> };
  'review.published': { review: Review };
  'review.deleted': { reviewId: string; productId: string };
  'media.uploaded': { reviewId: string; mediaItems: MediaItem[] };
}

class EventEmitter {
  emit<K extends keyof ReviewEvents>(event: K, data: ReviewEvents[K]): void;
  on<K extends keyof ReviewEvents>(event: K, handler: (data: ReviewEvents[K]) => void): void;
}
```

## Advanced Features

### **Smart Caching Strategy**
```typescript
// Multi-layer caching with intelligent invalidation
interface CacheStrategy {
  // L1: In-memory cache for hot data
  memory: {
    popular_products: Map<string, ProductStatistics>;
    recent_reviews: Map<string, Review>;
  };
  
  // L2: Redis cache for distributed caching
  redis: {
    product_statistics: string;      // TTL: 1 hour
    review_lists: string;           // TTL: 15 minutes
    translations: string;           // TTL: 24 hours
    user_preferences: string;       // TTL: 6 hours
  };
  
  // L3: CDN cache for static content
  cdn: {
    media_files: string;           // TTL: 30 days
    optimized_images: string;      // TTL: 7 days
  };
}
```

### **Background Job System**
```typescript
// Modern job processing with priorities and scheduling
interface JobSystem {
  // High priority jobs (real-time)
  statistics_update: {
    priority: 'high';
    retry_attempts: 3;
    timeout: 30000;
  };
  
  // Medium priority jobs (near real-time)
  translation: {
    priority: 'medium';
    retry_attempts: 5;
    timeout: 60000;
    batch_size: 10;
  };
  
  // Low priority jobs (background)
  media_optimization: {
    priority: 'low';
    retry_attempts: 2;
    timeout: 300000;
  };
  
  // Scheduled jobs
  analytics_aggregation: {
    schedule: '0 */6 * * *';        // Every 6 hours
    priority: 'low';
  };
}
```

## Performance & Scalability Goals

### **Response Time Targets**
- **Read Operations**: <50ms (cached), <200ms (database)
- **Write Operations**: <100ms (synchronous), background for heavy tasks
- **Media Upload**: <500ms for processing initiation
- **Search/Filter**: <150ms with proper indexing

### **Throughput Targets**
- **Reviews Creation**: 1000+ requests/second
- **Statistics Retrieval**: 5000+ requests/second
- **Concurrent Users**: 10,000+ simultaneous connections
- **Background Jobs**: 500+ jobs/second processing

### **Scalability Design**
- **Stateless Services**: Horizontal scaling ready
- **Database Sharding**: Product-based partitioning strategy
- **Cache Distribution**: Multi-region Redis clusters
- **CDN Integration**: Global media delivery

## Quality & Testing Standards

### **Test Coverage Requirements**
- **Unit Tests**: >95% coverage for business logic
- **Integration Tests**: All API endpoints and database operations
- **Performance Tests**: Load testing for all critical paths
- **Security Tests**: Input validation and authentication flows

### **Code Quality Standards**
- **TypeScript Strict Mode**: Full type safety
- **ESLint + Prettier**: Consistent code formatting
- **SonarQube**: Code quality and security analysis
- **Automated Reviews**: Pre-commit hooks and CI/CD integration

## Implementation Phases

### **Phase 1: Core Service Foundation**
1. Modern Express.js setup with TypeScript and middleware
2. DynamoDB schema design with optimal indexing strategy
3. Redis integration for caching and sessions
4. Basic CRUD operations with proper validation
5. Comprehensive error handling and logging

### **Phase 2: Advanced Features**
1. Media upload with S3 and image optimization
2. Translation service with intelligent caching
3. Real-time statistics calculation and caching
4. Background job processing system
5. Event-driven architecture implementation

### **Phase 3: Performance & Scale**
1. Multi-layer caching implementation
2. Database query optimization
3. API rate limiting and security
4. Performance monitoring and metrics
5. Load testing and optimization

### **Phase 4: Production Readiness**
1. Comprehensive monitoring and alerting
2. Security hardening and compliance
3. Documentation and API specifications
4. Deployment automation and CI/CD
5. Disaster recovery and backup strategies

## Success Metrics

### **Technical Metrics**
- **API Response Times**: 95th percentile under target thresholds
- **System Uptime**: 99.9% availability
- **Error Rates**: <0.1% for critical operations
- **Test Coverage**: >95% across all layers

### **Business Metrics**
- **Review Submission Rate**: Optimized user experience
- **Translation Accuracy**: High-quality multilingual content
- **Search Performance**: Fast, relevant results
- **Media Processing**: Efficient, high-quality optimization

---

**Your task is to build this modern, scalable reviews service focusing on clean architecture, optimal performance, and excellent developer experience. Prioritize code quality, comprehensive testing, and production-ready features.**