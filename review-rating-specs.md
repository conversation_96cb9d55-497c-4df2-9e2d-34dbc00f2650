# Reviews and Rating Service – Specification & Prompts

## 1. Service Overview
A scalable, high-performance microservice for managing product reviews and ratings, supporting rich media, multilingual content, analytics, and real-time updates for e-commerce.

## 1.1 Local Development Environment

To ensure easy onboarding and thorough local testing, the repository must include a comprehensive `docker-compose.yml` file with:

- Local DynamoDB instance
- DynamoDB Admin UI for database management
- S3-compatible storage service (e.g., MinIO) for local media handling
- All required service containers for seamless integration and testing

This setup allows developers to run the entire project locally, test all features, and maintain parity with cloud environments.


## 2. Core Features (Must Have)

### 2.1 Core Review Management System
- CRUD operations for reviews (create, read, update, delete)
- RESTful API endpoints with clear resource naming
- Review status: draft, published, moderated, rejected

### 2.2 Media Management and Optimization
- Upload, store, and optimize images/videos via S3 & CloudFront
- Compression, thumbnail generation, and CDN delivery

### 2.3 Performance and Scalability
- Sub-100ms response times for hot paths
- Horizontal scaling, stateless design, cache layers (memory, Redis, CDN)

### 2.4 Multilingual Translation Support
- Auto-detect language, translate via Google Translate API
- Store translations, confidence scoring, fallback logic

### 2.5 Real-time Rating Analytics
- Live calculation of product ratings, trends, and distributions
- WebSocket support for real-time updates

### 2.6 Data Migration from Legacy
- Tools and endpoints for migrating existing review data

### 2.7 Functional and Load Testing
- Pytest-based unit/integration tests
- Load testing for critical endpoints

### 2.8 API Design and Documentation
- OpenAPI/Swagger documentation
- Consistent response format (success, error, meta, pagination)

---

## 3. Should Have

### 3.1 Development Environment & Containerization
- Dockerized local development
- Environment parity with production

### 3.2 Background Job Processing
- AWS Lambda/SQS for async tasks (translation, analytics, media optimization)
- FastAPI background tasks for local jobs

### 3.3 Content Delivery Optimization
- CDN integration for media
- Cache warming and invalidation strategies

### 3.4 Verified Purchase Indicator
- Mark reviews from verified purchases

### 3.5 Monitoring and Observability
- Metrics, logging, and alerting for service health

---

## 4. Could Have

### 4.1 Advanced Search and Filtering
- Filter reviews by rating, date, tags, helpfulness

### 4.2 Review Helpfulness Voting
- Users can vote reviews as helpful/not helpful

### 4.3 Tagging System for Reviews
- Tag reviews for easier categorization

### 4.4 Auto-Moderation Pipeline
- Automated content moderation for reviews

### 4.5 Tag-Based Filtering
- Filter reviews by tags

### 4.6 Review History Logs
- Track changes and history for reviews

---

## 5. Won’t Have (Phase 1)

- User reputation score
- AI review summary display
- Emotion/voice detection in reviews
- Top reviewer badges
- Local review relevance
- Prompting reviews with context
- Customer Q&A
- Authentication & Authorization (assumed handled elsewhere)

---

## 6. API Endpoints (Sample Prompts)

- `POST /api/v1/reviews` – Create a new review
- `GET /api/v1/reviews/:id` – Get review details
- `PUT /api/v1/reviews/:id` – Update a review
- `DELETE /api/v1/reviews/:id` – Delete a review
- `GET /api/v1/reviews` – List reviews (with filters, pagination)
- `POST /api/v1/reviews/:id/media` – Upload media to a review
- `POST /api/v1/reviews/:id/translate` – Translate review content
- `GET /api/v1/products/:id/statistics` – Get product rating stats

---

## 7. Data Models (Prompts)

- **Review**: id, product_id, user_id, rating, content, language, translations, country_code, status, media_items, metadata, timestamps
- **ProductStatistics**: product_id, overall_rating, trends, demographics, last_updated
- **MediaItem**: id, type, original_url, optimized_urls, metadata, processing_status

---

## 8. Testing & Quality

- >95% unit test coverage for business logic
- Integration tests for all endpoints
- Performance/load tests for critical paths
- Security tests for input validation

---

## 9. Development Prompts

- “Implement a RESTful endpoint for creating a review with media upload and validation.”
- “Design a background job for translating reviews and updating translations in DynamoDB.”
- “Optimize review listing endpoint for sub-100ms response time using Redis caching.”
- “Add unit and integration tests for review CRUD operations.”

---

## 10. Success Metrics

- API response times under target thresholds
- 99.9% uptime
- <0.1% error rate for critical operations
- High review submission and translation accuracy

---

This specs/prompts document can be used as a reference for development, code reviews, and feature planning.
