version: '3.8'

services:
  # Main application
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
      - DYNAMODB_ENDPOINT_URL=http://dynamodb:8000
      - REDIS_URL=redis://redis:6379/0
      - S3_ENDPOINT_URL=http://minio:9000
      - CELERY_BROKER_URL=redis://redis:6379/1
    volumes:
      - .:/app
    depends_on:
      - dynamodb
      - redis
      - minio
    networks:
      - reviews-network

  # Local DynamoDB
  dynamodb:
    image: amazon/dynamodb-local:latest
    ports:
      - "8001:8000"
    command: ["-jar", "DynamoDBLocal.jar", "-sharedDb", "-dbPath", "/home/<USER>/data", "-optimizeDbBeforeStartup"]
    volumes:
      - dynamodb_data:/home/<USER>/data
    user: "0:0"
    environment:
      - JAVA_OPTS=-Xmx512m
    networks:
      - reviews-network

  # DynamoDB Admin UI
  dynamodb-admin:
    image: aaronshaf/dynamodb-admin:latest
    ports:
      - "8002:8001"
    environment:
      - DYNAMO_ENDPOINT=http://dynamodb:8000
    depends_on:
      - dynamodb
    networks:
      - reviews-network

  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - reviews-network

  # Redis Commander (Redis Admin UI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8003:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    platform: linux/amd64
    networks:
      - reviews-network

  # MinIO (S3-compatible storage)
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - reviews-network

  # Celery Worker for background jobs
  celery-worker:
    build: .
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - DEBUG=true
      - DYNAMODB_ENDPOINT_URL=http://dynamodb:8000
      - REDIS_URL=redis://redis:6379/0
      - S3_ENDPOINT_URL=http://minio:9000
      - CELERY_BROKER_URL=redis://redis:6379/1
    volumes:
      - .:/app
    depends_on:
      - redis
      - dynamodb
      - minio
    networks:
      - reviews-network

  # Celery Flower (Celery monitoring)
  celery-flower:
    build: .
    command: celery -A app.core.celery flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
    depends_on:
      - redis
    networks:
      - reviews-network

volumes:
  dynamodb_data:
  redis_data:
  minio_data:

networks:
  reviews-network:
    driver: bridge
