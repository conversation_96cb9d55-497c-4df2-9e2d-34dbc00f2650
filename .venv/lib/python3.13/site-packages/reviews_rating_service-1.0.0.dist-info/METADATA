Metadata-Version: 2.4
Name: reviews-rating-service
Version: 1.0.0
Summary: Modern, scalable reviews and ratings microservice
Author-email: Development Team <<EMAIL>>
License: MIT
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.8
Description-Content-Type: text/markdown

# Reviews and Rating Service

A modern, scalable microservice for managing product reviews and ratings built with FastAPI and Python.

## Features

- **Modern Review Management**: Complete CRUD operations with rich media support
- **Real-time Rating Analytics**: Live statistics calculation and trend analysis
- **Multilingual Support**: Auto-translation with Google Translate API
- **Media Management**: S3 integration with image/video optimization
- **High Performance**: Sub-100ms response times with multi-layer caching
- **Scalable Architecture**: Horizontal scaling with DynamoDB and Redis
- **Background Processing**: Async job processing with Celery
- **Comprehensive Testing**: >95% test coverage with Pytest

## Technology Stack

- **Runtime**: Python 3.8+
- **Framework**: FastAPI with Pydantic
- **Database**: AWS DynamoDB (primary) + Redis (caching)
- **Storage**: AWS S3 + CloudFront CDN
- **Background Jobs**: Celery + Redis
- **Testing**: Pytest with async support
- **Development**: Docker containers

## Quick Start

### Prerequisites

- Python 3.8+
- Docker and Docker Compose
- AWS credentials (for production)

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd reviews-rating-service
   ```

2. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start services with Docker Compose**
   ```bash
   docker-compose up -d
   ```

4. **Install dependencies (for local development)**
   ```bash
   pip install -r requirements.txt
   ```

5. **Run the application**
   ```bash
   uvicorn app.main:app --reload
   ```

### Available Services

- **API Server**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **DynamoDB Local**: http://localhost:8001
- **DynamoDB Admin**: http://localhost:8002
- **Redis**: localhost:6379
- **Redis Commander**: http://localhost:8003
- **MinIO (S3)**: http://localhost:9000
- **Celery Flower**: http://localhost:5555

## API Endpoints

### Reviews
- `POST /api/v1/reviews` - Create a new review
- `GET /api/v1/reviews/{id}` - Get review details
- `PUT /api/v1/reviews/{id}` - Update a review
- `DELETE /api/v1/reviews/{id}` - Delete a review
- `GET /api/v1/reviews` - List reviews with filters
- `POST /api/v1/reviews/{id}/translate` - Translate review content

### Statistics
- `GET /api/v1/products/{id}/statistics` - Get product rating stats
- `POST /api/v1/products/statistics/bulk` - Bulk statistics retrieval
- `GET /api/v1/products/{id}/statistics/trends` - Get rating trends

### Media
- `POST /api/v1/media/reviews/{id}/media` - Upload media to review
- `GET /api/v1/media/reviews/{id}/media` - Get review media
- `GET /api/v1/media/media/{id}` - Get specific media item
- `DELETE /api/v1/media/media/{id}` - Delete media item

## Configuration

Key environment variables:

```bash
# Application
APP_NAME=reviews-rating-service
DEBUG=true
LOG_LEVEL=INFO

# Database
DYNAMODB_ENDPOINT_URL=http://localhost:8000
REDIS_URL=redis://localhost:6379/0

# Storage
S3_BUCKET_NAME=reviews-media-bucket
S3_ENDPOINT_URL=http://localhost:9000

# Translation
GOOGLE_TRANSLATE_API_KEY=your_api_key

# Performance
MAX_UPLOAD_SIZE=10485760
CACHE_TTL_SECONDS=3600
RATE_LIMIT_PER_MINUTE=100
```

## Development

### Code Quality

```bash
# Format code
black app/
isort app/

# Lint code
flake8 app/
mypy app/

# Run tests
pytest

# Run tests with coverage
pytest --cov=app --cov-report=html
```

### Database Schema

The service uses DynamoDB with the following tables:

- **reviews**: Main review data with GSIs for product, user, and status queries
- **product_statistics**: Aggregated rating statistics per product
- **media_items**: Media file metadata with review associations

## Deployment

### Production Checklist

- [ ] Set `DEBUG=false`
- [ ] Configure AWS credentials
- [ ] Set up proper S3 bucket and CloudFront
- [ ] Configure Redis cluster
- [ ] Set up monitoring and alerting
- [ ] Configure proper CORS origins
- [ ] Set strong `SECRET_KEY`

### Docker Production

```bash
# Build production image
docker build -t reviews-service:latest .

# Run with production settings
docker run -p 8000:8000 --env-file .env.prod reviews-service:latest
```

## Performance Targets

- **Read Operations**: <50ms (cached), <200ms (database)
- **Write Operations**: <100ms (synchronous)
- **Media Upload**: <500ms (processing initiation)
- **Throughput**: 1000+ reviews/second, 5000+ statistics/second

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## License

MIT License - see LICENSE file for details.
